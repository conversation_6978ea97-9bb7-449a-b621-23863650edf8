<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <h1>🧹</h1>
          <h2>Temizlik Delisi</h2>
          <p>Admin Panel</p>
        </div>
      </div>

      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label for="email">E-posta</label>
          <input
            id="email"
            v-model="form.email"
            type="email"
            required
            :disabled="loading"
          />
        </div>

        <div class="form-group">
          <label for="password">Şifre</label>
          <input
            id="password"
            v-model="form.password"
            type="password"
            required
            :disabled="loading"
          />
        </div>

        <button type="submit" class="login-btn" :disabled="loading">
          <span v-if="loading">Giri<PERSON> yapılıyor...</span>
          <span v-else>Giriş Yap</span>
        </button>

        <div v-if="error" class="error-message">
          {{ error }}
        </div>
      </form>

      <div class="login-footer">
        <p>Temizlik Delisi Admin Panel v1.0</p>
      </div>
    </div>
  </div>
</template>

<script>
import apiService from '../services/api.js'

export default {
  name: 'Login',
  data() {
    return {
      form: {
        email: '',
        password: ''
      },
      loading: false,
      error: null
    }
  },
  mounted() {
    // Check if already logged in
    const token = localStorage.getItem('admin_token')
    if (token) {
      this.$router.push('/')
    }
  },
  methods: {
    async handleLogin() {
      this.loading = true
      this.error = null

      try {
        const API_BASE_URL = window.location.hostname === 'localhost'
          ? 'http://localhost:8000/api/v1'
          : `${window.location.protocol}//${window.location.host}/api/v1`

        const response = await fetch(`${API_BASE_URL}/admin/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json', 
            'admin_id': 'pVA99883xu'
          },
          body: JSON.stringify({
            identifier: this.form.email,
            password: this.form.password
          })
        })

        const data = await response.json()

        if (response.ok && data.IsSucceeded) {
          // Store token and expiration
          localStorage.setItem('admin_token', data.Token)
          localStorage.setItem('admin_token_expires', data.Expires)
          
          // Update API service token
          apiService.setToken(data.Token)
          
          // Redirect to dashboard
          this.$router.push('/')
        } else {
          this.error = data.error || 'Giriş başarısız'
        }
      } catch (error) {
        console.error('Login error:', error)
        this.error = 'Bağlantı hatası. Lütfen tekrar deneyin.'
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.login-header {
  margin-bottom: 40px;
}

.logo h1 {
  font-size: 3rem;
  margin: 0 0 10px 0;
}

.logo h2 {
  color: #2d3748;
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 5px 0;
}

.logo p {
  color: #718096;
  font-size: 1rem;
  margin: 0;
}

.login-form {
  text-align: left;
}

.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.9rem;
}

.form-group input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input:disabled {
  background-color: #f7fafc;
  cursor: not-allowed;
}

.login-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 16px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 20px;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.error-message {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  text-align: center;
  margin-top: 16px;
}

.login-footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.login-footer p {
  color: #a0aec0;
  font-size: 0.8rem;
  margin: 0;
}

@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
  }
  
  .logo h1 {
    font-size: 2.5rem;
  }
  
  .logo h2 {
    font-size: 1.5rem;
  }
}
</style>
