<template>
  <div id="app">
    <div v-if="!isLoginPage" class="container">
      <div class="header">
        <h1>Temizlik Delisi Admin Panel</h1>
        <nav class="nav">
          <router-link to="/">Dashboard</router-link>
          <router-link to="/customers">Müşteriler</router-link>
          <router-link to="/cleaners">Temizlikçiler</router-link>
          <router-link to="/categories">Kategoriler</router-link>
          <button @click="logout" class="logout-btn">Çıkış</button>
        </nav>
      </div>
      <router-view />
    </div>

    <router-view v-else />
  </div>
</template>

<script>
import apiService from './services/api.js'

export default {
  name: 'App',
  computed: {
    isLoginPage() {
      return this.$route.path === '/login'
    }
  },
  methods: {
    logout() {
      apiService.removeToken()
      this.$router.push('/login')
    }
  }
}
</script>