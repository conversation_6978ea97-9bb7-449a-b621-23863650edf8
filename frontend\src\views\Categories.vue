<template>
  <div>
    <div class="card">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <h2><PERSON><PERSON><PERSON></h2>
        <button @click="showCreateModal = true" class="btn btn-success">
          Yeni <PERSON>
        </button>
      </div>
      
      <div v-if="loading" class="loading">
        Yükleniyor...
      </div>

      <div v-else-if="error" class="error">
        {{ error }}
      </div>

      <div v-else>
        <table class="table">
          <thead>
            <tr>
              <th>ID</th>
              <th>Başlık</th>
              <th>Açıklama</th>
              <th>Ana <PERSON>gor<PERSON></th>
              <th>Onay <PERSON></th>
              <th>Oluşturulma Tarihi</th>
              <th><PERSON><PERSON><PERSON><PERSON></th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="category in categories" :key="category.id">
              <td>{{ category.id.substring(0, 8) }}...</td>
              <td>{{ category.title }}</td>
              <td>{{ category.description || 'Açıklama yok' }}</td>
              <td>
                <span :class="['badge', category.is_main ? 'badge-primary' : 'badge-secondary']">
                  {{ category.is_main ? 'Ana Kategori' : 'Alt Kategori' }}
                </span>
              </td>
              <td>
                <span :class="['badge', category.approved_by_admin ? 'badge-success' : 'badge-warning']">
                  {{ category.approved_by_admin ? 'Onaylandı' : 'Beklemede' }}
                </span>
              </td>
              <td>{{ formatDate(category.created_at) }}</td>
              <td>
                <button @click="editCategory(category)" class="btn btn-primary" style="margin-right: 5px;">
                  Düzenle
                </button>
                <button @click="deleteCategory(category.id)" class="btn btn-danger">
                  Sil
                </button>
              </td>
            </tr>
          </tbody>
        </table>

        <div v-if="categories.length === 0" style="text-align: center; padding: 40px; color: #666;">
          Henüz kategori bulunmuyor.
        </div>

        <!-- Pagination -->
        <div class="pagination" v-if="totalPages > 1">
          <button 
            @click="changePage(currentPage - 1)" 
            :disabled="currentPage === 1"
            class="btn"
          >
            Önceki
          </button>
          
          <button 
            v-for="page in visiblePages" 
            :key="page"
            @click="changePage(page)"
            :class="['btn', { active: page === currentPage }]"
          >
            {{ page }}
          </button>
          
          <button 
            @click="changePage(currentPage + 1)" 
            :disabled="currentPage === totalPages"
            class="btn"
          >
            Sonraki
          </button>
        </div>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <div v-if="showCreateModal || showEditModal" class="modal" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ showEditModal ? 'Kategori Düzenle' : 'Yeni Kategori Ekle' }}</h3>
          <button @click="closeModal" class="close">&times;</button>
        </div>
        
        <form @submit.prevent="saveCategory">
          <div class="form-group">
            <label>Başlık:</label>
            <input 
              v-model="categoryForm.title" 
              type="text" 
              class="form-control" 
              required
            >
          </div>
          
          <div class="form-group">
            <label>Açıklama:</label>
            <textarea 
              v-model="categoryForm.description" 
              class="form-control" 
              rows="3"
            ></textarea>
          </div>
          
          <div class="form-group">
            <label>
              <input 
                v-model="categoryForm.is_main" 
                type="checkbox"
                style="margin-right: 8px;"
              >
              Ana Kategori
            </label>
          </div>
          
          <div style="display: flex; gap: 10px; justify-content: flex-end;">
            <button type="button" @click="closeModal" class="btn">İptal</button>
            <button type="submit" class="btn btn-success" :disabled="saving">
              {{ saving ? 'Kaydediliyor...' : 'Kaydet' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import apiService from '../services/api.js'

export default {
  name: 'Categories',
  data() {
    return {
      categories: [],
      loading: true,
      error: null,
      currentPage: 1,
      perPage: 10,
      totalPages: 1,
      total: 0,
      showCreateModal: false,
      showEditModal: false,
      saving: false,
      categoryForm: {
        id: null,
        title: '',
        description: '',
        is_main: false
      }
    }
  },
  computed: {
    visiblePages() {
      const pages = []
      const start = Math.max(1, this.currentPage - 2)
      const end = Math.min(this.totalPages, this.currentPage + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    }
  },
  async mounted() {
    await this.loadCategories()
  },
  methods: {
    async loadCategories() {
      try {
        this.loading = true
        this.error = null
        
        const response = await apiService.getCategories(this.currentPage, this.perPage)
        
        if (response.data) {
          this.categories = response.data.rows || []
          this.totalPages = response.data.total_pages || 1
          this.total = response.data.total || 0
        }
      } catch (error) {
        this.error = 'Kategoriler yüklenirken bir hata oluştu: ' + error.message
        console.error('Error loading categories:', error)
      } finally {
        this.loading = false
      }
    },
    async changePage(page) {
      if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
        this.currentPage = page
        await this.loadCategories()
      }
    },
    editCategory(category) {
      this.categoryForm = {
        id: category.id,
        title: category.title,
        description: category.description || '',
        is_main: category.is_main
      }
      this.showEditModal = true
    },
    async saveCategory() {
      try {
        this.saving = true
        
        if (this.showEditModal) {
          await apiService.updateCategory(this.categoryForm)
        } else {
          await apiService.createCategory(this.categoryForm)
        }
        
        this.closeModal()
        await this.loadCategories()
      } catch (error) {
        this.error = 'Kategori kaydedilirken bir hata oluştu: ' + error.message
      } finally {
        this.saving = false
      }
    },
    async deleteCategory(categoryId) {
      if (confirm('Bu kategoriyi silmek istediğinizden emin misiniz?')) {
        try {
          await apiService.deleteCategory(categoryId)
          await this.loadCategories()
        } catch (error) {
          this.error = 'Kategori silinirken bir hata oluştu: ' + error.message
        }
      }
    },
    closeModal() {
      this.showCreateModal = false
      this.showEditModal = false
      this.categoryForm = {
        id: null,
        title: '',
        description: '',
        is_main: false
      }
    },
    formatDate(dateString) {
      if (!dateString) return 'Belirtilmemiş'
      const date = new Date(dateString)
      return date.toLocaleDateString('tr-TR')
    }
  }
}
</script>

<style scoped>
.badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.badge-success {
  background-color: #d4edda;
  color: #155724;
}

.badge-warning {
  background-color: #fff3cd;
  color: #856404;
}

.badge-primary {
  background-color: #cce7ff;
  color: #004085;
}

.badge-secondary {
  background-color: #e2e3e5;
  color: #383d41;
}
</style>
