package consts

const ( // User
	UserName = "username"
)

const ( //Error
	LoginFailed      = "giri<PERSON>şarısız, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	CreateUserFailed = "Kullanıcı Oluşturulamadı, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	UserNotFound     = "Kullanıcı Bulunamadı"
	UpdateUserFailed = "Kullanıcı Güncellenemedi, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
)

const (
	AlreadyExistComment         = "already_exist_comment"
	AlreadyExistService         = "already_exist_service"
	AlreadyExistFile            = "already_exist_file"
	AlreadyExistLanguage        = "already_exist_language"
	AlreadyExistOrder           = "already_exist_order"
	AlreadyExistCleaner         = "already_exist_cleaner"
	AlreadyExistCustomer        = "already_exist_customer"
	AlreadyExistAddress         = "already_exist_address"
	AlreadyExistOffer           = "already_exist_offer"
	AlreadyExistPreference      = "already_exist_preference"
	AlreadyExistServiceCategory = "already_exist_service_category"
	AlreadyExistWorkExperience  = "already_exist_work_experience"
)

const (
	NotFoundOrder           = "not_found_order"
	NotFoundBlog            = "not_found_blog"
	NotFoundComment         = "not_found_comment"
	NotFoundCleaner         = "not_found_cleaner"
	NotFoundCustomer        = "not_found_customer"
	NotFoundOffer           = "not_found_offer"
	NotFoundAddress         = "not_found_address"
	NotFoundPreference      = "not_found_preference"
	NotFoundServiceCategory = "not_found_service_category"
	NotFoundLanguage        = "not_found_language"
	NotFoundWorkExperience  = "not_found_work_experience"
)
