package message

import (
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/domains/message"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/localizer"
	"github.com/temizlik-delisi/pkg/mainlog"
	"github.com/temizlik-delisi/pkg/state"
)

// @Summary Get Centrifugo Connection Token
// @Description Get a JWT token for Centrifugo WebSocket connection
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} dtos.CentrifugoTokenResponseForSwagger
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /centrifugo/token [GET]
func GetCentrifugoToken(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.GenerateConnectionToken(c)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Centrifugo Token Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Centrifugo Token",
			Message:     "Centrifugo Token Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Start Conversation
// @Description Start a new conversation with another user
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param payload body dtos.StartConversationRequest true "Start conversation request"
// @Success 201 {object} dtos.ConversationsResponseForSwagger
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /conversation/start [POST]
func StartConversation(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.StartConversationRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Start Conversation Bind JSON Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		conversation_resp, err := s.StartConversation(c, &req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Start Conversation Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_create", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Start Conversation",
			Message:     "Conversation Started Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   conversation_resp,
			"status": 201,
		})
	}
}

// @Summary Get Conversations
// @Description Get all conversations for the current user
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param per_page query int false "Items per page" default(20)
// @Success 200 {object} dtos.PaginateResponseForSwagger
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /conversation [GET]
func GetConversations(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}

		resp, err := s.GetConversations(c, page, per_page)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Conversations Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Conversations",
			Message:     "Conversations Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Conversation
// @Description Get a specific conversation by ID
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Conversation ID"
// @Success 200 {object} dtos.ConversationsResponseForSwagger
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /conversation/{id} [GET]
func GetConversation(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		parsed_id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Conversation Parse UUID Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.GetConversation(c, parsed_id)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Conversation Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Conversation",
			Message:     "Conversation Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// -----> Message Endpoints Start

// @Summary Send Message
// @Description Send a message to another user
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param payload body dtos.SendMessageRequest true "Send message request"
// @Success 201 {object} dtos.MessageResponse
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /message/send [POST]
func SendMessage(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.SendMessageRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Send Message Bind JSON Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		response, err := s.SendMessage(c, &req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Send Message Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_create", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Send Message",
			Message:     "Message Sent Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   response,
			"status": 201,
		})
	}
}

// @Summary Get Messages
// @Description Get messages for a conversation
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param conversation_id query string true "Conversation ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} map[string]any
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /message/{id} [GET]
func GetMessages(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		log.Println("geldi")
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}
		parsed_id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Messages Parse UUID Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		messages, total, err := s.GetMessages(c, parsed_id, page, per_page)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Messages Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Messages",
			Message:     "Messages Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data": gin.H{
				"messages": messages,
				"total":    total,
				"page":     page,
				"limit":    per_page,
			},
			"status": 200,
		})
	}
}

// @Summary Mark Messages as Read
// @Description Mark messages in a conversation as read
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param payload body dtos.MarkAsReadRequest true "Mark as read request"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /message/read [POST]
func MarkMessagesAsRead(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.MarkAsReadRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Mark Messages as Read Bind JSON Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		err := s.MarkMessagesAsRead(c, &req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Mark Messages as Read Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Mark Messages as Read",
			Message:     "Messages Marked as Read Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   localizer.GetTranslated("message_marked_as_read", state.GetCurrentPhoneLanguage(c), nil),
			"status": 200,
		})
	}
}

// Message Endpoints End <-----
