s<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Centrifugo Test</title>
    <script src="https://unpkg.com/centrifuge@5.0.1/dist/centrifuge.js"></script>
</head>
<body>
    <h1>Centrifugo WebSocket Test</h1>
    <div id="status">Disconnected</div>
    <div id="messages"></div>
    <button onclick="connect()">Connect</button>
    <button onclick="disconnect()">Disconnect</button>
    
    <script>
        let centrifuge;
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        
        // Centrifugo token from our API
        const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkMTFkYWJhOC02MjcyLTQxMjEtYTZjZS00OGJlMGVmMmQ5ZjQiLCJleHAiOjE3NjY5MzIxODcsImlhdCI6MTc1OTE1NDk4N30.j05Ox5RnDRM41c3wRvZ5aalWdSkWWfazu_dAB6HpYsU";
        
        function addMessage(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            messagesDiv.appendChild(div);
        }
        
        function connect() {
            centrifuge = new Centrifuge('ws://localhost:8001/connection/websocket', {
                token: token
            });
            
            centrifuge.on('connecting', function(ctx) {
                addMessage('Connecting...');
                statusDiv.textContent = 'Connecting...';
            });
            
            centrifuge.on('connected', function(ctx) {
                addMessage('Connected to Centrifugo!');
                statusDiv.textContent = 'Connected';
                
                // Subscribe to personal channel
                const sub = centrifuge.newSubscription('personal:d11daba8-6272-4121-a6ce-48be0ef2d9f4');
                
                sub.on('publication', function(ctx) {
                    addMessage('Received message: ' + JSON.stringify(ctx.data));
                });
                
                sub.on('subscribing', function(ctx) {
                    addMessage('Subscribing to personal channel...');
                });
                
                sub.on('subscribed', function(ctx) {
                    addMessage('Subscribed to personal channel!');
                });
                
                sub.on('error', function(ctx) {
                    addMessage('Subscription error: ' + ctx.error);
                });
                
                sub.subscribe();
            });
            
            centrifuge.on('disconnected', function(ctx) {
                addMessage('Disconnected: ' + ctx.reason);
                statusDiv.textContent = 'Disconnected';
            });
            
            centrifuge.on('error', function(ctx) {
                addMessage('Connection error: ' + ctx.error);
                statusDiv.textContent = 'Error';
            });
            
            centrifuge.connect();
        }
        
        function disconnect() {
            if (centrifuge) {
                centrifuge.disconnect();
            }
        }
    </script>
</body>
</html>
