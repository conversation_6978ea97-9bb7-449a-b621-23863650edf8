<template>
  <div class="dashboard">
    <!-- Welcome Section -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">Admin Dashboard</h1>
        <p class="welcome-subtitle">Temizlik Delisi yönetim paneline hoş geldiniz</p>
      </div>
      <div class="welcome-actions">
        <button @click="testCors" class="btn btn-outline">Sistem Test</button>
      </div>
    </div>

    <!-- Stats Grid -->
    <div class="stats-grid">
      <div class="stat-card customers">
        <div class="stat-icon">
          <i class="icon-users"></i>
        </div>
        <div class="stat-content">
          <h3 class="stat-number">{{ customerCount }}</h3>
          <p class="stat-label">Toplam Müşteri</p>
          <router-link to="/customers" class="stat-link">Detayları Görüntüle →</router-link>
        </div>
      </div>

      <div class="stat-card cleaners">
        <div class="stat-icon">
          <i class="icon-cleaning"></i>
        </div>
        <div class="stat-content">
          <h3 class="stat-number">{{ cleanerCount }}</h3>
          <p class="stat-label">Toplam Temizlikçi</p>
          <router-link to="/cleaners" class="stat-link">Detayları Görüntüle →</router-link>
        </div>
      </div>

      <div class="stat-card categories">
        <div class="stat-icon">
          <i class="icon-category"></i>
        </div>
        <div class="stat-content">
          <h3 class="stat-number">{{ categoryCount }}</h3>
          <p class="stat-label">Toplam Kategori</p>
          <router-link to="/categories" class="stat-link">Detayları Görüntüle →</router-link>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="activity-section">
      <div class="card modern-card">
        <div class="card-header">
          <h2>Son Aktiviteler</h2>
          <span class="activity-badge">Canlı</span>
        </div>
        <div class="activity-list">
          <div class="activity-item">
            <div class="activity-dot new"></div>
            <div class="activity-content">
              <p class="activity-text">Sistem başarıyla çalışıyor</p>
              <span class="activity-time">Şimdi</span>
            </div>
          </div>
          <div class="activity-item">
            <div class="activity-dot"></div>
            <div class="activity-content">
              <p class="activity-text">Admin paneli hazır</p>
              <span class="activity-time">2 dakika önce</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import apiService from '../services/api.js'

export default {
  name: 'Dashboard',
  data() {
    return {
      customerCount: 0,
      cleanerCount: 0,
      categoryCount: 0,
      loading: true
    }
  },
  async mounted() {
    await this.loadStats()
  },
  methods: {
    async loadStats() {
      try {
        // Load basic stats
        const [customers, cleaners, categories] = await Promise.all([
          apiService.getCustomers(1, 1),
          apiService.getCleaners(1, 1),
          apiService.getCategories(1, 1)
        ])

        this.customerCount = customers.data?.total || 0
        this.cleanerCount = cleaners.data?.total || 0
        this.categoryCount = categories.data?.total || 0
      } catch (error) {
        console.error('Error loading stats:', error)
      } finally {
        this.loading = false
      }
    },
    async testCors() {
      try {
        console.log('Testing CORS...')
        const API_BASE_URL = window.location.hostname === 'localhost'
          ? 'http://localhost:8000/api/v1'
          : `${window.location.protocol}//${window.location.host}/api/v1`

        const response = await fetch(`${API_BASE_URL}/admin/test`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'admin_id': 'pVA99883xu'
          }
        })
        const data = await response.json()
        console.log('CORS Test Success:', data)
        alert('CORS Test Başarılı: ' + data.message)
      } catch (error) {
        console.error('CORS Test Error:', error)
        alert('CORS Test Hatası: ' + error.message)
      }
    }
  }
}
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  border-radius: 16px;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.welcome-content p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.btn-outline {
  background: transparent;
  border: 2px solid rgba(255,255,255,0.3);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s;
}

.btn-outline:hover {
  background: rgba(255,255,255,0.1);
  border-color: rgba(255,255,255,0.5);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.stat-card.customers::before {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.cleaners::before {
  background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);
}

.stat-card.categories::before {
  background: linear-gradient(90deg, #fa709a 0%, #fee140 100%);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  font-size: 24px;
}

.customers .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.cleaners .stat-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.categories .stat-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.icon-users::before { content: "👥"; }
.icon-cleaning::before { content: "🧹"; }
.icon-category::before { content: "📂"; }

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #2d3748;
}

.stat-label {
  font-size: 1rem;
  color: #718096;
  margin: 0 0 16px 0;
}

.stat-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: color 0.3s;
}

.stat-link:hover {
  color: #5a67d8;
}

.activity-section {
  margin-top: 30px;
}

.modern-card {
  border-radius: 16px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.card-header h2 {
  margin: 0;
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 600;
}

.activity-badge {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.activity-list {
  space-y: 16px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f7fafc;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e2e8f0;
  margin-top: 6px;
  flex-shrink: 0;
}

.activity-dot.new {
  background: #43e97b;
  box-shadow: 0 0 0 4px rgba(67, 233, 123, 0.2);
}

.activity-content {
  flex: 1;
}

.activity-text {
  margin: 0 0 4px 0;
  color: #2d3748;
  font-weight: 500;
}

.activity-time {
  color: #718096;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .welcome-content h1 {
    font-size: 2rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
