package dtos

import (
	"time"

	"github.com/google/uuid"
)

// SendMessageRequest represents the request to send a message
type SendMessageRequest struct {
	ReceiverID  uuid.UUID `json:"receiver_id" binding:"required" example:"123e4567-e89b-12d3-a456-************"`
	Content     string    `json:"content" binding:"required" example:"Hello, how are you?"`
	MessageType string    `json:"message_type" example:"text"` // text, image, file
	FileURL     string    `json:"file_url,omitempty" example:"https://example.com/file.jpg"`
	FileName    string    `json:"file_name,omitempty" example:"image.jpg"`
}

// MessageResponse represents a message in API responses
type MessageResponse struct {
	ID          uuid.UUID  `json:"id" example:"123e4567-e89b-12d3-a456-************"`
	SenderID    uuid.UUID  `json:"sender_id" example:"123e4567-e89b-12d3-a456-************"`
	ReceiverID  uuid.UUID  `json:"receiver_id" example:"123e4567-e89b-12d3-a456-************"`
	Content     string     `json:"content" example:"Hello, how are you?"`
	MessageType string     `json:"message_type" example:"text"`
	FileURL     string     `json:"file_url,omitempty" example:"https://example.com/file.jpg"`
	FileName    string     `json:"file_name,omitempty" example:"image.jpg"`
	IsRead      bool       `json:"is_read" example:"false"`
	ReadAt      *time.Time `json:"read_at,omitempty" example:"2023-01-01T12:00:00Z"`
	ChannelName string     `json:"channel_name" example:"private:customer-id:cleaner-id"`
	CreatedAt   time.Time  `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt   time.Time  `json:"updated_at" example:"2023-01-01T12:00:00Z"`

	// Sender info
	SenderName     string `json:"sender_name,omitempty" example:"John Doe"`
	SenderEmail    string `json:"sender_email,omitempty" example:"<EMAIL>"`
	SenderPhotoURL string `json:"sender_photo_url,omitempty" example:"https://example.com/photo.jpg"`
}

// ConversationResponse represents a conversation in API responses
type ConversationResponse struct {
	ID            uuid.UUID        `json:"id" example:"123e4567-e89b-12d3-a456-************"`
	CustomerID    uuid.UUID        `json:"customer_id" example:"123e4567-e89b-12d3-a456-************"`
	CleanerID     uuid.UUID        `json:"cleaner_id" example:"123e4567-e89b-12d3-a456-************"`
	ChannelName   string           `json:"channel_name" example:"private:customer-id:cleaner-id"`
	LastMessage   *MessageResponse `json:"last_message,omitempty"`
	LastMessageAt *time.Time       `json:"last_message_at,omitempty" example:"2023-01-01T12:00:00Z"`
	UnreadCount   int              `json:"unread_count" example:"3"`
	CreatedAt     time.Time        `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt     time.Time        `json:"updated_at" example:"2023-01-01T12:00:00Z"`

	// Other participant info
	ParticipantName     string `json:"participant_name" example:"Jane Smith"`
	ParticipantEmail    string `json:"participant_email" example:"<EMAIL>"`
	ParticipantPhotoURL string `json:"participant_photo_url,omitempty" example:"https://example.com/photo.jpg"`
}

type ConversationsResponseForSwagger struct {
	Data   ConversationResponse `json:"data"`
	Status int                  `json:"status" example:"200"`
}

// GetMessagesRequest represents the request to get messages
type GetMessagesRequest struct {
	ConversationID uuid.UUID `json:"conversation_id" form:"conversation_id" binding:"required" example:"123e4567-e89b-12d3-a456-************"`
	Page           int       `json:"page" form:"page" example:"1"`
	Limit          int       `json:"limit" form:"limit" example:"20"`
}

// MarkAsReadRequest represents the request to mark messages as read
type MarkAsReadRequest struct {
	ConversationID uuid.UUID `json:"conversation_id" binding:"required" example:"123e4567-e89b-12d3-a456-************"`
}

// CentrifugoTokenRequest represents the request to get Centrifugo connection token
type CentrifugoTokenRequest struct {
	UserID uuid.UUID `json:"user_id" binding:"required" example:"123e4567-e89b-12d3-a456-************"`
}

type CentrifugoTokenResponse struct {
	Token string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
}

type CentrifugoTokenResponseForSwagger struct {
	Data   CentrifugoTokenResponse `json:"data"`
	Status int                     `json:"status" example:"200"`
}

// CentrifugoMessage represents a message sent through Centrifugo
type CentrifugoMessage struct {
	Type         string                `json:"type" example:"message"`
	Message      *MessageResponse      `json:"message,omitempty"`
	Conversation *ConversationResponse `json:"conversation,omitempty"`
	Event        string                `json:"event,omitempty" example:"new_message"`
}

// StartConversationRequest represents the request to start a new conversation
type StartConversationRequest struct {
	ParticipantID  uuid.UUID `json:"participant_id" binding:"required" example:"123e4567-e89b-12d3-a456-************"`
	InitialMessage string    `json:"initial_message,omitempty" example:"Hello, I would like to book your service"`
}
