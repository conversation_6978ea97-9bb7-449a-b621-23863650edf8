import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '../views/Dashboard.vue'
import Customers from '../views/Customers.vue'
import Cleaners from '../views/Cleaners.vue'
import Categories from '../views/Categories.vue'
import CustomerDetail from '../views/CustomerDetail.vue'
import CleanerDetail from '../views/CleanerDetail.vue'
import Login from '../views/Login.vue'

// Auth guard function
function requireAuth(to, from, next) {
  const token = localStorage.getItem('admin_token')
  const expires = localStorage.getItem('admin_token_expires')

  if (!token || !expires || new Date() >= new Date(expires)) {
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_token_expires')
    next('/login')
  } else {
    next()
  }
}

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard,
    beforeEnter: requireAuth
  },
  {
    path: '/customers',
    name: 'Customers',
    component: Customers,
    beforeEnter: requireAuth
  },
  {
    path: '/customers/:id',
    name: 'CustomerDetail',
    component: CustomerDetail,
    beforeEnter: requireAuth
  },
  {
    path: '/cleaners',
    name: 'Cleaners',
    component: Cleaners,
    beforeEnter: requireAuth
  },
  {
    path: '/cleaners/:id',
    name: 'CleanerDetail',
    component: CleanerDetail,
    beforeEnter: requireAuth
  },
  {
    path: '/categories',
    name: 'Categories',
    component: Categories,
    beforeEnter: requireAuth
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
