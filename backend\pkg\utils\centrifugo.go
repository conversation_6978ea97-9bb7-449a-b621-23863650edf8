package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/centrifugal/gocent/v3"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/config"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/state"
)

var (
	centrifugoClient *gocent.Client
	centrifugoOnce   sync.Once
)

// CentrifugoWrapper wraps Centrifugo client functionality
type CentrifugoWrapper struct {
	client *gocent.Client
	config config.Centrifugo
}

// CentrifugoClaims represents JWT claims for Centrifugo authentication
type CentrifugoClaims struct {
	Sub string `json:"sub"` // User ID
	jwt.RegisteredClaims
}

// InitCentrifugo initializes the Centrifugo client
func InitCentrifugo() *CentrifugoWrapper {
	centrifugoOnce.Do(func() {
		cfg := config.InitConfig().Centrifugo

		centrifugoClient = gocent.New(gocent.Config{
			Addr: cfg.URL,
			Key:  cfg.APIKey,
		})
	})

	return &CentrifugoWrapper{
		client: centrifugoClient,
		config: config.InitConfig().Centrifugo,
	}
}

// GenerateConnectionToken generates a JWT token for Centrifugo connection
func (c *CentrifugoWrapper) GenerateConnectionToken(ctx context.Context) (string, error) {
	now := time.Now()
	claims := &CentrifugoClaims{
		Sub: state.GetCurrentID(ctx).String(),
		RegisteredClaims: jwt.RegisteredClaims{
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(time.Duration(c.config.TokenTTL) * time.Second)),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(c.config.Secret))
	if err != nil {
		return "", fmt.Errorf("failed to generate token: %w", err)
	}

	return tokenString, nil
}

// PublishMessage publishes a message to a specific channel
func (c *CentrifugoWrapper) PublishMessage(ctx context.Context, channel string, message *dtos.CentrifugoMessage) error {
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	_, err = c.client.Publish(ctx, channel, data)
	if err != nil {
		return fmt.Errorf("failed to publish message: %w", err)
	}

	log.Printf("Message published to channel %s", channel)
	return nil
}

// SubscribeUserToChannel subscribes a user to a specific channel
func (c *CentrifugoWrapper) SubscribeUserToChannel(ctx context.Context, userID uuid.UUID, channel string) error {
	err := c.client.Subscribe(ctx, channel, userID.String())
	if err != nil {
		return fmt.Errorf("failed to subscribe user %s to channel %s: %w", userID.String(), channel, err)
	}

	log.Printf("User %s subscribed to channel %s", userID.String(), channel)
	return nil
}

// UnsubscribeUserFromChannel unsubscribes a user from a specific channel
func (c *CentrifugoWrapper) UnsubscribeUserFromChannel(ctx context.Context, userID uuid.UUID, channel string) error {
	err := c.client.Unsubscribe(ctx, channel, userID.String())
	if err != nil {
		return fmt.Errorf("failed to unsubscribe user %s from channel %s: %w", userID.String(), channel, err)
	}

	log.Printf("User %s unsubscribed from channel %s", userID.String(), channel)
	return nil
}

// GetChannelPresence gets the list of users currently present in a channel
func (c *CentrifugoWrapper) GetChannelPresence(ctx context.Context, channel string) (*gocent.PresenceResult, error) {
	result, err := c.client.Presence(ctx, channel)
	if err != nil {
		return nil, fmt.Errorf("failed to get channel presence: %w", err)
	}

	return &result, nil
}

// DisconnectUser disconnects a specific user from Centrifugo
func (c *CentrifugoWrapper) DisconnectUser(ctx context.Context, userID uuid.UUID) error {
	err := c.client.Disconnect(ctx, userID.String())
	if err != nil {
		return fmt.Errorf("failed to disconnect user %s: %w", userID.String(), err)
	}

	log.Printf("User %s disconnected", userID.String())
	return nil
}

// BroadcastToChannel broadcasts a message to all users in a channel
func (c *CentrifugoWrapper) BroadcastToChannel(ctx context.Context, channel string, message *dtos.CentrifugoMessage) error {
	return c.PublishMessage(ctx, channel, message)
}

// SendPrivateMessage sends a private message between two users
func (c *CentrifugoWrapper) SendPrivateMessage(ctx context.Context, senderID, receiverID uuid.UUID, messageResponse *dtos.MessageResponse) error {
	// Generate channel name for private conversation
	channelName := generatePrivateChannelName(senderID, receiverID)

	centrifugoMessage := &dtos.CentrifugoMessage{
		Type:    "message",
		Message: messageResponse,
		Event:   "new_message",
	}

	return c.PublishMessage(ctx, channelName, centrifugoMessage)
}

// NotifyConversationUpdate notifies users about conversation updates (like new conversation, read status, etc.)
func (c *CentrifugoWrapper) NotifyConversationUpdate(ctx context.Context, userID uuid.UUID, conversation *dtos.ConversationResponse) error {
	// Send to user's personal channel for conversation list updates
	personalChannel := fmt.Sprintf("personal:%s", userID.String())

	centrifugoMessage := &dtos.CentrifugoMessage{
		Type:         "conversation_update",
		Conversation: conversation,
		Event:        "conversation_updated",
	}

	return c.PublishMessage(ctx, personalChannel, centrifugoMessage)
}

// generatePrivateChannelName creates a consistent channel name for private conversations
func generatePrivateChannelName(userID1, userID2 uuid.UUID) string {
	// Sort IDs to ensure consistent channel names
	if userID1.String() < userID2.String() {
		return fmt.Sprintf("private:%s:%s", userID1.String(), userID2.String())
	}
	return fmt.Sprintf("private:%s:%s", userID2.String(), userID1.String())
}

// ValidateConnectionToken validates a Centrifugo connection token
func (c *CentrifugoWrapper) ValidateConnectionToken(tokenString string) (*CentrifugoClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &CentrifugoClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(c.config.Secret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if claims, ok := token.Claims.(*CentrifugoClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}
