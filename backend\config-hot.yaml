app:
  name: temiz<PERSON>-delisi
  port: 8000
  host:
  environment: local
  jwt_issuer: "temizlik-delisi"
  jwt_secret: "3nDYJfp2n7"
  jwt_expire: 30
  client_id: kar19B2SaS
  admin_id: pVA99883xu
  onesignal_api_key: 123456
  force_update_key: 85V54eAvUx
  admin_email: "<EMAIL>"
  admin_password: "QnKH1v6H831wa14H"
google:
  client_ids:
    - 1042805366786-qtjs31fllj9gm9p1supj58fomo7kt3k3.apps.googleusercontent.com
    - 1042805366786-inn5jjlua2bl6tq81ipdc3ri4aptll8g.apps.googleusercontent.com
  redirect_url: https://temizlik-delisi-mobile.firebaseapp.com/__/auth/handler
apple:
  client_id: com.temizlikdelisi.app
  team_id: YOUR_TEAM_ID
  key_id: YOUR_KEY_ID
  private_key: |
    -----B<PERSON>IN PRIVATE KEY-----
    YOUR_PRIVATE_KEY_CONTENT_HERE
    -----END PRIVATE KEY-----
  redirect_url: https://your-app.com/apple-callback

redis:
  host: temizlik-delisi-redis
  port: 6379
  pass: a9315Rrlj5NQ
database:
  host: temizlik-delisi-db
  port: 5432
  user: temizlik-delisi
  pass: piDP434yr3D7JcAEh3
  name: temizlik-delisi-db
cloudinary:
  name: xxx
  api_key: xxx
  api_secret: xxx
  api_folder: xxx
allows:
  methods:
  - GET
  - POST
  - PUT
  - PATCH
  - DELETE
  - OPTIONS
  headers:
  - Content-Type
  - Authorization
  - X-CSRF-Token
  - data-api-key
  - admin_id
  - client_id
  origins:
    - http://localhost:8000
    - http://localhost:9000
    - http://localhost:4040
    - http://localhost:3000
whatsapp:
  api_key: xxx
centrifugo:
  url: http://temizlik-delisi-centrifugo:8000/api
  api_key: temizlik_delisi_centrifugo_api_key_2024
  secret: temizlik_delisi_centrifugo_secret_2024
  token_ttl: 7777200