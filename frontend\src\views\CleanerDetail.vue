<template>
  <div class="cleaner-detail">
    <div class="detail-header">
      <button @click="goBack" class="back-btn">
        ← Geri <PERSON>
      </button>
      <h1>Temizlikçi Detayları</h1>
    </div>

    <div v-if="loading" class="loading">
      Yükleniyor...
    </div>

    <div v-else-if="error" class="error">
      {{ error }}
    </div>

    <div v-else-if="cleaner" class="detail-content">
      <!-- Profile Section -->
      <div class="profile-section">
        <div class="profile-card">
          <div class="profile-header">
            <div class="profile-avatar">
              <img v-if="cleaner.profile_photo_url" :src="cleaner.profile_photo_url" :alt="cleaner.name" />
              <span v-else class="avatar-placeholder">{{ getInitials(cleaner.name, cleaner.surname) }}</span>
            </div>
            <div class="profile-info">
              <h2>{{ cleaner.name }} {{ cleaner.surname }}</h2>
              <p class="profile-email">{{ cleaner.email }}</p>
              <div class="verification-status">
                <span :class="['verification-badge', cleaner.tc_no_verified ? 'verified' : 'pending']">
                  {{ cleaner.tc_no_verified ? '✓ TC Doğrulandı' : '⏳ TC Beklemede' }}
                </span>
                <span :class="['verification-badge', cleaner.criminal_record_document_verified ? 'verified' : 'pending']">
                  {{ cleaner.criminal_record_document_verified ? '✓ Adli Sicil Doğrulandı' : '⏳ Adli Sicil Beklemede' }}
                </span>
              </div>
              <div class="profile-stats">
                <div class="stat">
                  <span class="stat-value">{{ cleaner.total_point }}</span>
                  <span class="stat-label">Toplam Puan</span>
                </div>
                <div class="stat">
                  <span class="stat-value">{{ cleaner.total_comment }}</span>
                  <span class="stat-label">Yorum Sayısı</span>
                </div>
                <div class="stat">
                  <span class="stat-value">{{ cleaner.average_rate?.toFixed(1) || '0.0' }}</span>
                  <span class="stat-label">Ortalama Puan</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Details Grid -->
      <div class="details-grid">
        <!-- Personal Information -->
        <div class="detail-card">
          <h3>Kişisel Bilgiler</h3>
          <div class="detail-items">
            <div class="detail-item">
              <span class="label">Ad Soyad:</span>
              <span class="value">{{ cleaner.name }} {{ cleaner.surname }}</span>
            </div>
            <div class="detail-item">
              <span class="label">E-posta:</span>
              <span class="value">{{ cleaner.email }}</span>
            </div>
            <div class="detail-item">
              <span class="label">Telefon:</span>
              <span class="value">{{ cleaner.phone || 'Belirtilmemiş' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">Doğum Tarihi:</span>
              <span class="value">{{ cleaner.date_of_birth || 'Belirtilmemiş' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">Ülke:</span>
              <span class="value">{{ cleaner.country || 'Belirtilmemiş' }}</span>
            </div>
          </div>
        </div>

        <!-- Verification Information -->
        <div class="detail-card">
          <h3>Doğrulama Bilgileri</h3>
          <div class="detail-items">
            <div class="detail-item">
              <span class="label">TC Kimlik No:</span>
              <span class="value">
                {{ cleaner.tc_no ? '***********' : 'Belirtilmemiş' }}
                <span v-if="cleaner.tc_no" :class="['status-badge', cleaner.tc_no_verified ? 'verified' : 'pending']">
                  {{ cleaner.tc_no_verified ? 'Doğrulandı' : 'Beklemede' }}
                </span>
              </span>
            </div>
            <div class="detail-item">
              <span class="label">Adli Sicil Belgesi:</span>
              <span class="value">
                {{ cleaner.criminal_record_document_filename || 'Yüklenmemiş' }}
                <span v-if="cleaner.criminal_record_document_filename" :class="['status-badge', cleaner.criminal_record_document_verified ? 'verified' : 'pending']">
                  {{ cleaner.criminal_record_document_verified ? 'Doğrulandı' : 'Beklemede' }}
                </span>
              </span>
            </div>
            <div class="detail-item" v-if="cleaner.criminal_record_document_url">
              <span class="label">Belge Linki:</span>
              <span class="value">
                <a :href="cleaner.criminal_record_document_url" target="_blank" class="document-link">
                  Belgeyi Görüntüle
                </a>
              </span>
            </div>
          </div>
        </div>

        <!-- Account Information -->
        <div class="detail-card">
          <h3>Hesap Bilgileri</h3>
          <div class="detail-items">
            <div class="detail-item">
              <span class="label">Temizlikçi ID:</span>
              <span class="value id-value">
                {{ cleaner.id }}
                <button @click="copyId(cleaner.id)" class="copy-btn-small">📋</button>
              </span>
            </div>
            <div class="detail-item">
              <span class="label">Hesap Türü:</span>
              <span class="value">
                <span class="account-badge cleaner">Temizlikçi</span>
              </span>
            </div>
            <div class="detail-item">
              <span class="label">Kayıt Tarihi:</span>
              <span class="value">{{ formatDate(cleaner.created_at) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">Profil Güncellenmiş:</span>
              <span class="value">
                <span :class="['status-badge', cleaner.is_profile_updated ? 'updated' : 'not-updated']">
                  {{ cleaner.is_profile_updated ? 'Evet' : 'Hayır' }}
                </span>
              </span>
            </div>
            <div class="detail-item">
              <span class="label">Sağlayıcı:</span>
              <span class="value">{{ cleaner.provider || 'Belirtilmemiş' }}</span>
            </div>
          </div>
        </div>

        <!-- App Information -->
        <div class="detail-card">
          <h3>Uygulama Bilgileri</h3>
          <div class="detail-items">
            <div class="detail-item">
              <span class="label">Telefon Dili:</span>
              <span class="value">{{ cleaner.phone_language || 'Belirtilmemiş' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">Zaman Dilimi:</span>
              <span class="value">{{ cleaner.time_zone || 'Belirtilmemiş' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">İşletim Sistemi:</span>
              <span class="value">{{ cleaner.os || 'Belirtilmemiş' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">Son Versiyon:</span>
              <span class="value">{{ cleaner.last_version_name || 'Belirtilmemiş' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">Son Giriş:</span>
              <span class="value">{{ formatDate(cleaner.last_login_date) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import apiService from '../services/api.js'

export default {
  name: 'CleanerDetail',
  data() {
    return {
      cleaner: null,
      loading: true,
      error: null
    }
  },
  async mounted() {
    await this.loadCleaner()
  },
  methods: {
    async loadCleaner() {
      try {
        this.loading = true
        this.error = null
        
        const cleanerId = this.$route.params.id
        // TODO: Implement get cleaner by ID API
        // For now, we'll simulate loading
        setTimeout(() => {
          this.cleaner = {
            id: cleanerId,
            name: 'Örnek',
            surname: 'Temizlikçi',
            email: '<EMAIL>',
            phone: '+90 ************',
            date_of_birth: '1985-05-15',
            country: 'Türkiye',
            tc_no: '12345678901',
            tc_no_verified: true,
            criminal_record_document_filename: 'adli_sicil.pdf',
            criminal_record_document_url: 'https://example.com/documents/adli_sicil.pdf',
            criminal_record_document_verified: false,
            total_point: 280,
            total_comment: 12,
            average_rate: 4.8,
            is_profile_updated: true,
            provider: 'google',
            phone_language: 'TR',
            time_zone: 'Europe/Istanbul',
            os: 'ios',
            last_version_name: '1.0.0',
            last_login_date: '2024-01-15T14:20:00Z',
            created_at: '2023-03-10T09:15:00Z',
            profile_photo_url: null
          }
          this.loading = false
        }, 1000)
        
      } catch (error) {
        this.error = 'Temizlikçi bilgileri yüklenirken bir hata oluştu: ' + error.message
        console.error('Error loading cleaner:', error)
      } finally {
        this.loading = false
      }
    },
    goBack() {
      this.$router.push('/cleaners')
    },
    getInitials(name, surname) {
      const firstInitial = name ? name.charAt(0).toUpperCase() : ''
      const lastInitial = surname ? surname.charAt(0).toUpperCase() : ''
      return firstInitial + lastInitial
    },
    copyId(id) {
      navigator.clipboard.writeText(id).then(() => {
        alert('ID kopyalandı: ' + id)
      }).catch(() => {
        const textArea = document.createElement('textarea')
        textArea.value = id
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        alert('ID kopyalandı: ' + id)
      })
    },
    formatDate(dateString) {
      if (!dateString) return 'Belirtilmemiş'
      const date = new Date(dateString)
      return date.toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.cleaner-detail {
  max-width: 1200px;
  margin: 0 auto;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
}

.back-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
}

.back-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.detail-header h1 {
  margin: 0;
  color: #2d3748;
  font-size: 2rem;
  font-weight: 700;
}

.profile-section {
  margin-bottom: 30px;
}

.profile-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: 1px solid #f0f0f0;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 24px;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
  font-weight: 700;
  font-size: 2.5rem;
  flex-shrink: 0;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info h2 {
  margin: 0 0 8px 0;
  color: #2d3748;
  font-size: 2rem;
  font-weight: 700;
}

.profile-email {
  color: #718096;
  font-size: 1.1rem;
  margin: 0 0 16px 0;
}

.verification-status {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.verification-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.verification-badge.verified {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.verification-badge.pending {
  background: linear-gradient(135deg, #ffa726 0%, #ffcc02 100%);
  color: white;
}

.profile-stats {
  display: flex;
  gap: 32px;
}

.stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #43e97b;
  margin-bottom: 4px;
}

.stat-label {
  color: #718096;
  font-size: 0.9rem;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.detail-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: 1px solid #f0f0f0;
}

.detail-card h3 {
  margin: 0 0 20px 0;
  color: #2d3748;
  font-size: 1.25rem;
  font-weight: 600;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
}

.detail-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f7fafc;
}

.detail-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #4a5568;
  min-width: 140px;
}

.value {
  color: #2d3748;
  font-weight: 500;
  text-align: right;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
}

.id-value {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  word-break: break-all;
}

.copy-btn-small {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
  font-size: 12px;
}

.copy-btn-small:hover {
  background-color: #e2e8f0;
}

.account-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.account-badge.cleaner {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.verified {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.status-badge.pending {
  background: linear-gradient(135deg, #ffa726 0%, #ffcc02 100%);
  color: white;
}

.status-badge.updated {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.status-badge.not-updated {
  background: linear-gradient(135deg, #ffa726 0%, #ffcc02 100%);
  color: white;
}

.document-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.document-link:hover {
  color: #5a67d8;
  text-decoration: underline;
}

@media (max-width: 768px) {
  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .detail-header h1 {
    font-size: 1.5rem;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
  }

  .profile-avatar {
    width: 100px;
    height: 100px;
    font-size: 2rem;
  }

  .profile-stats {
    justify-content: center;
    gap: 24px;
  }

  .verification-status {
    justify-content: center;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .value {
    text-align: left;
    justify-content: flex-start;
  }
}
</style>
