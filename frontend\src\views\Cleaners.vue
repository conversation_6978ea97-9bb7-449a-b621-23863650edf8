<template>
  <div>
    <div class="card">
      <h2>Temizlikçi Listesi</h2>
      
      <div v-if="loading" class="loading">
        Yükleniyor...
      </div>

      <div v-else-if="error" class="error">
        {{ error }}
      </div>

      <div v-else>
        <table class="table modern-table">
          <thead>
            <tr>
              <th>ID</th>
              <th>Ad Soyad</th>
              <th>Email</th>
              <th>TC Doğrulama</th>
              <th><PERSON><PERSON><PERSON></th>
              <th>Toplam Puan</th>
              <th>İşlemler</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="cleaner in cleaners" :key="cleaner.id">
              <td>
                <div class="id-cell">
                  <span class="id-text">{{ cleaner.id.substring(0, 6) }}...</span>
                  <button @click="copyId(cleaner.id)" class="copy-btn" title="ID'y<PERSON>">
                    📋
                  </button>
                </div>
              </td>
              <td>
                <div class="user-info">
                  <div class="user-avatar">
                    <img v-if="cleaner.profile_photo_url" :src="cleaner.profile_photo_url" :alt="cleaner.name" />
                    <span v-else class="avatar-placeholder">{{ getInitials(cleaner.name, cleaner.surname) }}</span>
                  </div>
                  <span class="user-name">{{ cleaner.name }} {{ cleaner.surname }}</span>
                </div>
              </td>
              <td>{{ cleaner.email }}</td>
              <td>
                <span :class="['verification-badge', cleaner.tc_no_verified ? 'verified' : 'pending']">
                  {{ cleaner.tc_no_verified ? 'Doğrulandı' : 'Beklemede' }}
                </span>
              </td>
              <td>{{ formatDate(cleaner.created_at) }}</td>
              <td>
                <span class="point-badge">{{ cleaner.total_point }}</span>
              </td>
              <td>
                <div class="action-buttons">
                  <button @click="viewCleaner(cleaner)" class="btn-action btn-view" title="Görüntüle">
                    👁️
                  </button>
                  <button @click="deleteCleaner(cleaner.id)" class="btn-action btn-delete" title="Sil">
                    🗑️
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <div v-if="cleaners.length === 0" style="text-align: center; padding: 40px; color: #666;">
          Henüz temizlikçi bulunmuyor.
        </div>

        <!-- Pagination -->
        <div class="pagination" v-if="totalPages > 1">
          <button 
            @click="changePage(currentPage - 1)" 
            :disabled="currentPage === 1"
            class="btn"
          >
            Önceki
          </button>
          
          <button 
            v-for="page in visiblePages" 
            :key="page"
            @click="changePage(page)"
            :class="['btn', { active: page === currentPage }]"
          >
            {{ page }}
          </button>
          
          <button 
            @click="changePage(currentPage + 1)" 
            :disabled="currentPage === totalPages"
            class="btn"
          >
            Sonraki
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import apiService from '../services/api.js'

export default {
  name: 'Cleaners',
  data() {
    return {
      cleaners: [],
      loading: true,
      error: null,
      currentPage: 1,
      perPage: 10,
      totalPages: 1,
      total: 0
    }
  },
  computed: {
    visiblePages() {
      const pages = []
      const start = Math.max(1, this.currentPage - 2)
      const end = Math.min(this.totalPages, this.currentPage + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    }
  },
  async mounted() {
    await this.loadCleaners()
  },
  methods: {
    async loadCleaners() {
      try {
        this.loading = true
        this.error = null
        
        const response = await apiService.getCleaners(this.currentPage, this.perPage)
        
        if (response.data) {
          this.cleaners = response.data.rows || []
          this.totalPages = response.data.total_pages || 1
          this.total = response.data.total || 0
        }
      } catch (error) {
        this.error = 'Temizlikçiler yüklenirken bir hata oluştu: ' + error.message
        console.error('Error loading cleaners:', error)
      } finally {
        this.loading = false
      }
    },
    async changePage(page) {
      if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
        this.currentPage = page
        await this.loadCleaners()
      }
    },
    formatDate(dateString) {
      if (!dateString) return 'Belirtilmemiş'
      const date = new Date(dateString)
      return date.toLocaleDateString('tr-TR')
    },
    copyId(id) {
      navigator.clipboard.writeText(id).then(() => {
        alert('ID kopyalandı: ' + id)
      }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea')
        textArea.value = id
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        alert('ID kopyalandı: ' + id)
      })
    },
    getInitials(name, surname) {
      const firstInitial = name ? name.charAt(0).toUpperCase() : ''
      const lastInitial = surname ? surname.charAt(0).toUpperCase() : ''
      return firstInitial + lastInitial
    },
    viewCleaner(cleaner) {
      this.$router.push(`/cleaners/${cleaner.id}`)
    },
    async deleteCleaner(cleanerId) {
      if (confirm('Bu temizlikçiyi silmek istediğinizden emin misiniz?')) {
        try {
          // TODO: API call for delete
          console.log('Deleting cleaner:', cleanerId)
          alert('Temizlikçi silme özelliği henüz aktif değil')
        } catch (error) {
          console.error('Error deleting cleaner:', error)
          alert('Temizlikçi silinirken hata oluştu')
        }
      }
    }
  }
}
</script>

<style scoped>
.modern-table {
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.modern-table th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  padding: 16px;
  text-align: left;
  border: none;
}

.modern-table td {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
}

.modern-table tr:hover {
  background-color: #f8faff;
}

.id-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.id-text {
  font-family: 'Courier New', monospace;
  background: #f7fafc;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #4a5568;
}

.copy-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
  font-size: 14px;
}

.copy-btn:hover {
  background-color: #e2e8f0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  font-size: 14px;
  font-weight: 600;
}

.user-name {
  font-weight: 500;
  color: #2d3748;
}

.verification-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.verification-badge.verified {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.verification-badge.pending {
  background: linear-gradient(135deg, #ffa726 0%, #ffcc02 100%);
  color: white;
}

.point-badge {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-action {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s;
}

.btn-view {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.btn-view:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
}

.btn-delete {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
}

.btn-delete:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

@media (max-width: 768px) {
  .modern-table {
    font-size: 0.9rem;
  }

  .modern-table th,
  .modern-table td {
    padding: 12px 8px;
  }

  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
}
</style>
