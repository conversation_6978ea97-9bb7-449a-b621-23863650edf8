package message

import (
	"github.com/gin-gonic/gin"
	"github.com/temizlik-delisi/pkg/domains/message"
	"github.com/temizlik-delisi/pkg/middleware"
)

func MessageRoutes(r *gin.RouterGroup, s message.Service) {
	m := r.Group("/message")
	c := r.Group("/conversation")
	centrifugo := r.Group("/centrifugo")

	// Apply middleware for authentication and account type validation
	m.Use(middleware.FromClient(), middleware.Authorized(), middleware.AccountType(1, 2))
	c.Use(middleware.FromClient(), middleware.Authorized(), middleware.AccountType(1, 2))
	centrifugo.Use(middleware.FromClient(), middleware.Authorized(), middleware.AccountType(1, 2))
	{
		m.POST("/send", SendMessage(s))
		m.GET("/:id", GetMessages(s))
		m.POST("/read", MarkMessagesAsRead(s))

		c.POST("/start", StartConversation(s))
		c.GET("", GetConversations(s))
		c.GET("/:id", GetConversation(s))

		centrifugo.GET("/token", GetCentrifugoToken(s))
	}
}
