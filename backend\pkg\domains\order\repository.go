package order

import (
	"context"
	"errors"
	"fmt"
	"log"
	"math"
	"time"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/consts"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
	"github.com/temizlik-delisi/pkg/utils"
	"gorm.io/gorm"
)

type Repository interface {
	getOrders(ctx context.Context, page, per_page, account_type, account_point int, radiusMeters, minRange, maxRange float64, start_date, end_date, room_number, order_type, search string) (*dtos.PaginatedData, error)
	getMyOrders(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
	getByID(ctx context.Context, id uuid.UUID) (*entities.OrderResponse, error)
	createOrder(ctx context.Context, req dtos.RequestForCreateOrder) (dtos.ResponseForCreateOrder, error)
	deleteOrder(ctx context.Context, id uuid.UUID) error
	updateOrder(ctx context.Context, req dtos.RequestForUpdateOrder) error
	updateOrderStatusByCron(ctx context.Context) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) getOrders(ctx context.Context, page, per_page, account_type, account_point int, radiusMeters, minRange, maxRange float64, start_date, end_date, room_number, order_type, search string) (*dtos.PaginatedData, error) {
	var (
		orders               []entities.Order
		resp                 []entities.OrderResponse
		count                int64
		account_address      entities.Address
		current_license      entities.License
		permittedCategoryIDs []string
	)

	// find service categories except main categories
	var service_categories []entities.ServiceCategory
	if err := r.db.WithContext(ctx).
		Model(&entities.ServiceCategory{}).
		Where("approved_by_admin = ?", true).
		Where("is_main = ?", false).
		Find(&service_categories).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).
		Model(&entities.License{}).
		Where("account_id = ?", state.GetCurrentID(ctx)).
		Order("created_at desc").
		First(&current_license).Error; err != nil {
		return nil, err
	}

	base_query := r.db.WithContext(ctx).Table("orders o")

	for _, v := range service_categories {
		for _, l := range v.PermittedLisanceTypes {
			if int(l) == current_license.Type {
				permittedCategoryIDs = append(permittedCategoryIDs, v.ID.String())
				break
			}
		}
	}

	if len(permittedCategoryIDs) > 0 {
		base_query = base_query.Where("o.service_category_id IN ?", permittedCategoryIDs)
	}

	if current_license.Type == 3 {

		if radiusMeters != 0 {
			// isteği atan account'un adresini bul
			r.db.WithContext(ctx).
				Model(&entities.Address{}).
				Where("account_id = ?", state.GetCurrentID(ctx)).
				Where("account_type = ?", state.GetCurrentAccountType(ctx)).
				First(&account_address)

			point := fmt.Sprintf("SRID=4326;POINT(%f %f)", account_address.Lng, account_address.Lat)

			base_query = base_query.
				Select(`o.*, ST_Distance(a.location, ST_GeogFromText(?)) AS distance`, point).
				Joins("JOIN addresses a ON a.id = o.address_id").
				Where("ST_DWithin(a.location, ST_GeogFromText(?), ?)", point, radiusMeters)
		}

		if account_point != 0 {

			switch account_type {
			case 1:
				base_query = base_query.
					Joins("JOIN customers c ON c.id = o.account_id").
					Where("c.average_rate >= ?", account_point)
			case 2:
				base_query = base_query.
					Joins("JOIN cleaners cl ON cl.id = o.account_id").
					Where("cl.average_rate >= ?", account_point)
			}
		}

		if start_date != "" && end_date != "" {
			base_query = base_query.Where("o.start_date BETWEEN ? AND ?", start_date, end_date)
		}

		if minRange != 0 && maxRange != 0 {
			base_query = base_query.Where("o.total_price BETWEEN ? AND ?", minRange, maxRange)
		}

		if room_number != "" && false {
			base_query = base_query.Where("o.house_size IN ?", room_number)
		}
	}

	base_query = base_query.Where("o.account_type = ?", account_type).
		Where("o.account_id != ?", state.GetCurrentID(ctx)).
		Where("o.status IN ?", []int{1, 2}).
		Where("o.id NOT IN (?)",
			r.db.Table("offers").
				Select("order_id").
				Where("bidder_id = ?", state.GetCurrentID(ctx)).
				Where("bidder_account_type = ?", state.GetCurrentAccountType(ctx)))

	if search != "" {
		base_query = base_query.Where("(o.title LIKE ? OR o.description LIKE ?)", "%"+search+"%", "%"+search+"%")
	}

	switch order_type {
	case "distance":
		base_query = base_query.Order("distance ASC")
	case "license":
		base_query = base_query.Order("license_type DESC")
	default:
		base_query = base_query.Order("created_at DESC")
	}

	if err := base_query.Debug().
		Limit(per_page).
		Offset((page - 1) * per_page).
		Find(&orders).Error; err != nil {
		return nil, err
	}

	for _, v := range orders {
		resp = append(resp, v.Response(r.db))
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}

func (r *repository) getMyOrders(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	var (
		orders []entities.Order
		resp   []entities.OrderResponse
		count  int64
	)
	base_query := r.db.WithContext(ctx).
		Model(&entities.Order{}).
		Where("account_id = ?", state.GetCurrentID(ctx)).
		Where("account_type = ?", state.GetCurrentAccountType(ctx))

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Order("created_at desc").
		Find(&orders).Error; err != nil {
		return nil, err
	}

	for _, v := range orders {
		resp = append(resp, v.Response(r.db))
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}

func (r *repository) getByID(ctx context.Context, id uuid.UUID) (*entities.OrderResponse, error) {
	var (
		order           entities.Order
		resp            entities.OrderResponse
		current_license entities.License
	)

	if err := r.db.WithContext(ctx).
		Model(&entities.License{}).
		Where("account_id = ?", state.GetCurrentID(ctx)).
		Order("created_at desc").
		First(&current_license).Error; err != nil {
		return &resp, err
	}

	r.db.WithContext(ctx).
		Model(&entities.Order{}).
		Where("id = ?", id).
		First(&order)

	if order.ID == uuid.Nil {
		return &resp, errors.New(consts.NotFoundOrder)
	}

	if current_license.Type == 1 && order.AccountType != state.GetCurrentAccountType(ctx) {
		return &resp, errors.New("you_cannot_see_this_order_with_this_license")

	}

	if !r.canDoItWithCurrentLicense(ctx, current_license.Type, order.ServiceCategoryID) {
		return &resp, errors.New("you_cannot_see_this_order_with_this_license")
	}

	resp = order.Response(r.db)

	return &resp, nil
}

func (r *repository) createOrder(ctx context.Context, req dtos.RequestForCreateOrder) (dtos.ResponseForCreateOrder, error) {
	var (
		resp            dtos.ResponseForCreateOrder
		current_service entities.ServiceCategory
		current_address entities.Address
		current_license entities.License
	)

	if err := r.db.WithContext(ctx).
		Model(&entities.License{}).
		Where("account_id = ?", state.GetCurrentID(ctx)).
		Order("created_at desc").
		First(&current_license).Error; err != nil {
		return resp, err
	}

	r.db.WithContext(ctx).
		Model(&entities.ServiceCategory{}).
		Where("id = ?", req.ServiceCategoryID).
		First(&current_service)

	if current_service.ID == uuid.Nil {
		return resp, errors.New(consts.NotFoundServiceCategory)
	}

	if !r.canDoItWithCurrentLicense(ctx, current_license.Type, current_service.ID) {
		return resp, errors.New("you_cannot_create_order_with_this_license")
	}

	r.db.WithContext(ctx).
		Model(&entities.Address{}).
		Where("account_id = ?", state.GetCurrentID(ctx)).
		Where("account_type = ?", state.GetCurrentAccountType(ctx)).
		Order("created_at desc").
		First(&current_address)

	if current_address.ID == uuid.Nil {
		return resp, errors.New(consts.NotFoundAddress)
	}

	// control start-end time

	var min_x_hour_after_current_time int = 0
	if current_license.Type == 1 {
		min_x_hour_after_current_time = 48
	} else {
		min_x_hour_after_current_time = 4
	}

	if utils.MinXHourAfterCurrentTime(min_x_hour_after_current_time, req.StartDate) {
		return resp, errors.New("start_date_cannot_be_less_than_x_hour_after_current_time")
	}

	if utils.CannotBeBeforeCurrentTime(req.StartDate, req.EndDate) {
		return resp, errors.New("start_date_cannot_be_before_current_time")
	}

	if utils.CannotBeBeforeStartTime(req.StartDate, req.EndDate) {
		return resp, errors.New("end_date_cannot_be_before_start_time")
	}

	if utils.DifferenceBetweenTimes(req.StartDate, req.EndDate) < time.Duration(current_service.MinDuration) {
		return resp, errors.New("duration_cannot_be_less_than_min_duration")
	}

	order := &entities.Order{
		Title:             req.Title,
		Description:       req.Description,
		StartDate:         req.StartDate,
		EndDate:           req.EndDate,
		PricePerCleaner:   req.PricePerCleaner,
		TotalPrice:        req.PricePerCleaner * float64(req.HowManyCleaners),
		HowManyCleaners:   req.HowManyCleaners,
		HouseSize:         req.HouseSize,
		ServiceCategoryID: uuid.MustParse(req.ServiceCategoryID),
		RoomNumbers:       req.RoomNumbers,
		AddressID:         current_address.ID,
		AccountID:         state.GetCurrentID(ctx),
		AccountType:       state.GetCurrentAccountType(ctx),
		LicenseType:       current_license.Type,
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Order{}).
		Create(order).Error

	resp.OrderID = order.ID

	return resp, err
}

func (r *repository) canDoItWithCurrentLicense(ctx context.Context, license_type int, service_category_id uuid.UUID) bool {
	var service_category entities.ServiceCategory
	r.db.WithContext(ctx).
		Model(&entities.ServiceCategory{}).
		Where("id = ?", service_category_id).
		First(&service_category)

	for _, v := range service_category.PermittedLisanceTypes {
		if v == int64(license_type) {
			return true
		}
	}
	return false
}

func (r *repository) deleteOrder(ctx context.Context, id uuid.UUID) error {
	var current_order entities.Order
	r.db.WithContext(ctx).
		Model(&entities.Order{}).
		Where("id = ?", id).
		Where("account_id = ?", state.GetCurrentID(ctx)).
		First(&current_order)

	if current_order.ID == uuid.Nil {
		return errors.New(consts.NotFoundOrder)
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Order{}).
		Delete(&current_order).Error

	return err
}

func (r *repository) updateOrder(ctx context.Context, req dtos.RequestForUpdateOrder) error {
	var (
		current_order   entities.Order
		address_control entities.Address
		service_control entities.ServiceCategory
		current_license entities.License
	)

	if err := r.db.WithContext(ctx).
		Model(&entities.License{}).
		Where("account_id = ?", state.GetCurrentID(ctx)).
		Order("created_at desc").
		First(&current_license).Error; err != nil {
		return err
	}

	r.db.WithContext(ctx).
		Model(&entities.Order{}).
		Where("id = ?", req.ID).
		Where("account_id = ?", state.GetCurrentID(ctx)).
		First(&current_order)

	if current_order.ID == uuid.Nil {
		return errors.New(consts.NotFoundOrder)
	}

	r.db.WithContext(ctx).
		Model(&entities.Address{}).
		Where("account_id = ?", state.GetCurrentID(ctx)).
		Where("account_type = ?", state.GetCurrentAccountType(ctx)).
		Order("created_at desc").
		First(&address_control)

	if address_control.ID == uuid.Nil {
		return errors.New(consts.NotFoundAddress)
	}

	r.db.WithContext(ctx).
		Model(&entities.ServiceCategory{}).
		Where("id = ?", req.ServiceCategoryID).
		First(&service_control)

	if service_control.ID == uuid.Nil {
		return errors.New(consts.NotFoundServiceCategory)
	}

	if !r.canDoItWithCurrentLicense(ctx, current_license.Type, service_control.ID) {
		return errors.New("you_cannot_create_order_with_this_license")
	}

	var min_x_hour_after_current_time int = 0
	if current_license.Type == 1 {
		min_x_hour_after_current_time = 48
	} else {
		min_x_hour_after_current_time = 4
	}

	if utils.MinXHourAfterCurrentTime(min_x_hour_after_current_time, req.StartDate) {
		return errors.New("start_date_cannot_be_less_than_x_hour_after_current_time")
	}

	if utils.CannotBeBeforeCurrentTime(req.StartDate, req.EndDate) {
		return errors.New("start_date_cannot_be_before_current_time")
	}
	if utils.CannotBeBeforeStartTime(req.StartDate, req.EndDate) {
		return errors.New("end_date_cannot_be_before_start_time")
	}
	if utils.DifferenceBetweenTimes(req.StartDate, req.EndDate) < time.Duration(service_control.MinDuration) {
		return errors.New("duration_cannot_be_less_than_min_duration")
	}

	if err := r.db.WithContext(ctx).
		Model(&entities.Order{}).
		Where("id = ?", req.ID).
		Where("account_id = ?", state.GetCurrentID(ctx)).
		Updates(entities.Order{
			Title:             req.Title,
			Description:       req.Description,
			StartDate:         req.StartDate,
			EndDate:           req.EndDate,
			PricePerCleaner:   req.PricePerCleaner,
			TotalPrice:        req.PricePerCleaner * float64(req.HowManyCleaners),
			HowManyCleaners:   req.HowManyCleaners,
			RoomNumbers:       req.RoomNumbers,
			HouseSize:         req.HouseSize,
			ServiceCategoryID: uuid.MustParse(req.ServiceCategoryID),
			AddressID:         address_control.ID,
		}).Error; err != nil {
		return err
	}

	return nil
}

// updateOrderStatusByCron - Cron job ile order statuslarını günceller
func (r *repository) updateOrderStatusByCron(ctx context.Context) error {
	now := time.Now()

	// Status 4: Cleaning in Progress - Şu anda temizlik zamanı aralığında olan siparişler
	// StartDate <= now <= EndDate olan ve status'u 3 (Cleaner Assigned) olan siparişleri 4'e çek
	if err := r.db.WithContext(ctx).
		Model(&entities.Order{}).
		Where("status = ?", 3). // Cleaner Assigned
		Where("start_date <= ?", now).
		Where("end_date >= ?", now).
		Update("status", 4).Error; err != nil {
		log.Printf("Error updating orders to status 4 (Cleaning in Progress): %v", err)
		return err
	}

	// Status 5: Cleaning Completed - Temizlik zamanı geçmiş olan siparişler
	// EndDate < now olan ve status'u 4 (Cleaning in Progress) olan siparişleri 5'e çek
	if err := r.db.WithContext(ctx).
		Model(&entities.Order{}).
		Where("status = ?", 4). // Cleaning in Progress
		Where("end_date < ?", now).
		Update("status", 5).Error; err != nil {
		log.Printf("Error updating orders to status 5 (Cleaning Completed): %v", err)
		return err
	}

	log.Printf("Order status update completed at %v", now.Format("2006-01-02 15:04:05"))
	return nil
}
