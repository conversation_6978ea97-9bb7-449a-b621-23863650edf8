# Build stage
FROM node:18-alpine as build-stage

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM caddy:alpine as production-stage

# Copy built files to caddy
COPY --from=build-stage /app/dist /srv

# Create Caddyfile
RUN echo ":80 { root * /srv; file_server; try_files {path} /index.html }" > /etc/caddy/Caddyfile

# Expose port 80
EXPOSE 80

# Start caddy
CMD ["caddy", "run", "--config", "/etc/caddy/Caddyfile"]
