# Temizlik Delisi Admin Panel

Bu proje, Temizlik Delisi uygulaması için basit bir admin paneli içerir.

## Özellikler

- **Dashboard**: <PERSON>l istatistikler ve hızlı erişim
- **Müşteri Listesi**: Tüm müşterileri görüntüleme ve sayfalama
- **Temizlikçi Listesi**: Tüm temizlikçileri görüntüleme ve TC doğrulama durumu
- **Kategori Yönetimi**: <PERSON><PERSON> kate<PERSON> oluşturma, düzenleme ve silme

## Kurulum

### Docker ile (Önerilen)

#### Development Mode (Test Ortamı)
```bash
# Backend klasöründen
docker-compose -f docker-compose-hot.yml up --build
```

#### Production Mode
```bash
# Backend klasöründen
docker-compose up --build temizlik-delisi-admin
```

### Manuel Kurulum

1. Bağımlılıkları yükleyin:
```bash
npm install
```

2. Geliştirme sunucusunu başlatın:
```bash
npm run dev
```

3. Tarayıcınızda `http://localhost:3000` adresine gidin.

## Kullanılan Teknolojiler

- **Vue.js 3**: Frontend framework
- **Vue Router**: Sayfa yönlendirme
- **Vite**: Build tool
- **Vanilla CSS**: Styling

## API Endpoints

Admin paneli aşağıdaki backend endpointlerini kullanır:

- `GET /api/v1/admin/customer/get-all` - Müşteri listesi
- `GET /api/v1/admin/cleaner/get-all` - Temizlikçi listesi  
- `GET /api/v1/admin/service-category/get-all` - Kategori listesi
- `POST /api/v1/admin/service-category/create` - Kategori oluşturma
- `POST /api/v1/admin/service-category/update` - Kategori güncelleme
- `POST /api/v1/admin/service-category/delete` - Kategori silme

## Yapılandırma

API base URL'i `src/services/api.js` dosyasında değiştirilebilir:

```javascript
const API_BASE_URL = 'http://localhost:8000/api/v1'
```

## Build

Production build için:

```bash
npm run build
```

Build dosyaları `dist/` klasörüne oluşturulur.

## Notlar

- Admin paneli basit bir yapıya sahiptir ve temel CRUD işlemlerini destekler
- Authentication henüz implement edilmemiştir
- Responsive tasarım temel seviyededir
- Hata yönetimi basit seviyededir
