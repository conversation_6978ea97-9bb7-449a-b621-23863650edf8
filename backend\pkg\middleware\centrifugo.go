package middleware

import (
	"fmt"
	"log"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/utils"
)

// CentrifugoAuthRequest represents the authentication request from Centrifugo
type CentrifugoAuthRequest struct {
	Token string `json:"token"`
}

// CentrifugoAuthResponse represents the authentication response to Centrifugo
type CentrifugoAuthResponse struct {
	Result *CentrifugoAuthResult `json:"result,omitempty"`
	Error  *CentrifugoError      `json:"error,omitempty"`
}

type CentrifugoAuthResult struct {
	User     string                 `json:"user"`
	Channels []string               `json:"channels,omitempty"`
	Info     map[string]interface{} `json:"info,omitempty"`
}

type CentrifugoError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// CentrifugoAuth handles authentication requests from Centrifugo
func CentrifugoAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		log.Println("Centrifugo auth request received")
		var req CentrifugoAuthRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			log.Printf("Failed to bind JSON in CentrifugoAuth: %v", err)
			c.JSON(http.StatusOK, CentrifugoAuthResponse{
				Error: &CentrifugoError{
					Code:    400,
					Message: "Invalid request format",
				},
			})
			return
		}

		// If no token provided, allow anonymous connection
		if req.Token == "" {
			log.Println("Anonymous connection allowed")
			c.JSON(http.StatusOK, CentrifugoAuthResponse{
				Result: &CentrifugoAuthResult{
					User:     "anonymous",
					Channels: []string{}, // No channels for anonymous users
					Info: map[string]interface{}{
						"user_id": "anonymous",
					},
				},
			})
			return
		}

		// Validate the token
		centrifugo := utils.InitCentrifugo()
		claims, err := centrifugo.ValidateConnectionToken(req.Token)
		if err != nil {
			log.Printf("Token validation failed in CentrifugoAuth: %v", err)
			c.JSON(http.StatusOK, CentrifugoAuthResponse{
				Error: &CentrifugoError{
					Code:    401,
					Message: "Invalid or expired token",
				},
			})
			return
		}

		userID, err := uuid.Parse(claims.Sub)
		if err != nil {
			c.JSON(http.StatusOK, CentrifugoAuthResponse{
				Error: &CentrifugoError{
					Code:    401,
					Message: "Invalid user ID in token",
				},
			})
			return
		}

		// Generate user's allowed channels
		channels := generateUserChannels(userID)

		// Return successful authentication
		c.JSON(http.StatusOK, CentrifugoAuthResponse{
			Result: &CentrifugoAuthResult{
				User:     userID.String(),
				Channels: channels,
				Info: map[string]interface{}{
					"user_id": userID.String(),
				},
			},
		})
	}
}

// CentrifugoSubscribe handles subscription requests from Centrifugo
func CentrifugoSubscribe() gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			User    string `json:"user"`
			Channel string `json:"channel"`
		}

		log.Printf("Centrifugo subscribe request received for channel: %s", req.Channel)

		if err := c.ShouldBindJSON(&req); err != nil {
			log.Printf("Failed to bind JSON in CentrifugoSubscribe: %v", err)
			c.JSON(http.StatusOK, map[string]interface{}{
				"error": map[string]interface{}{
					"code":    400,
					"message": "Invalid request format",
				},
			})
			return
		}

		log.Printf("Subscribe request for user: %s, channel: %s", req.User, req.Channel)

		userID, err := uuid.Parse(req.User)
		if err != nil {
			log.Printf("Invalid user ID in CentrifugoSubscribe: %s", req.User)
			c.JSON(http.StatusOK, map[string]interface{}{
				"error": map[string]interface{}{
					"code":    401,
					"message": "Invalid user ID",
				},
			})
			return
		}

		// Check if user is allowed to subscribe to this channel
		if !isUserAllowedToSubscribe(userID, req.Channel) {
			log.Printf("Access denied for user %s to channel %s", userID.String(), req.Channel)
			c.JSON(http.StatusOK, map[string]interface{}{
				"error": map[string]interface{}{
					"code":    403,
					"message": "Access denied to channel",
				},
			})
			return
		}

		log.Printf("Subscription allowed for user %s to channel %s", userID.String(), req.Channel)

		// Allow subscription
		c.JSON(http.StatusOK, map[string]interface{}{
			"result": map[string]interface{}{},
		})
	}
}

// CentrifugoPublish handles publish requests from Centrifugo (optional, for additional validation)
func CentrifugoPublish() gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			User    string      `json:"user"`
			Channel string      `json:"channel"`
			Data    interface{} `json:"data"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusOK, map[string]interface{}{
				"error": map[string]interface{}{
					"code":    400,
					"message": "Invalid request format",
				},
			})
			return
		}

		userID, err := uuid.Parse(req.User)
		if err != nil {
			c.JSON(http.StatusOK, map[string]interface{}{
				"error": map[string]interface{}{
					"code":    401,
					"message": "Invalid user ID",
				},
			})
			return
		}

		// Check if user is allowed to publish to this channel
		if !isUserAllowedToPublish(userID, req.Channel) {
			c.JSON(http.StatusOK, map[string]interface{}{
				"error": map[string]interface{}{
					"code":    403,
					"message": "Access denied to publish to channel",
				},
			})
			return
		}

		// Allow publish
		c.JSON(http.StatusOK, map[string]interface{}{
			"result": map[string]interface{}{},
		})
	}
}

// generateUserChannels generates the list of channels a user is allowed to access
func generateUserChannels(userID uuid.UUID) []string {
	channels := []string{
		fmt.Sprintf("personal:%s", userID.String()), // Personal channel for notifications
	}

	// Note: Private conversation channels are dynamically allowed in isUserAllowedToSubscribe
	// We don't pre-generate them here as there could be many

	return channels
}

// isUserAllowedToSubscribe checks if a user is allowed to subscribe to a specific channel
func isUserAllowedToSubscribe(userID uuid.UUID, channel string) bool {
	// Personal channel
	personalChannel := fmt.Sprintf("personal:%s", userID.String())
	if channel == personalChannel {
		return true
	}

	// Private conversation channels
	if strings.HasPrefix(channel, "private:") {
		// Extract user IDs from channel name: private:user1:user2
		parts := strings.Split(channel, ":")
		if len(parts) == 3 {
			user1ID := parts[1]
			user2ID := parts[2]

			// User can subscribe if they are one of the participants
			if user1ID == userID.String() || user2ID == userID.String() {
				return true
			}
		}
	}

	return false
}

// isUserAllowedToPublish checks if a user is allowed to publish to a specific channel
func isUserAllowedToPublish(userID uuid.UUID, channel string) bool {
	// For now, we don't allow direct publishing from clients
	// All messages should go through our API endpoints
	// This provides better control and validation
	return false
}

// CentrifugoWebhookAuth validates Centrifugo webhook requests
func CentrifugoWebhookAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// For proxy mode, Centrifugo doesn't send Authorization header
		// We'll validate based on the request coming from the correct source
		log.Printf("Centrifugo proxy request from: %s", c.ClientIP())

		// In proxy mode, we trust requests coming from Centrifugo container
		// In production, you should add more security checks like:
		// - IP whitelist
		// - Custom headers
		// - Request signing

		log.Println("Centrifugo proxy request allowed")
		c.Next()
	}
}
