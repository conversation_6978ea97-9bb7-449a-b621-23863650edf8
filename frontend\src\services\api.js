// Dinamik API base URL - production'da aynı domain, development'ta localhost
const API_BASE_URL = window.location.hostname === 'localhost'
  ? 'http://localhost:8000/api/v1'
  : `${window.location.protocol}//${window.location.host}/api/v1`

class ApiService {
  constructor() {
    this.token = localStorage.getItem('admin_token')
  }

  setToken(token) {
    this.token = token
    localStorage.setItem('admin_token', token)
  }

  removeToken() {
    this.token = null
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_token_expires')
  }

  isTokenExpired() {
    const expires = localStorage.getItem('admin_token_expires')
    if (!expires) return true
    return new Date() >= new Date(expires)
  }

  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
      'admin_id': 'pVA99883xu'
    }

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`
    }

    return headers
  }

  async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`

    const defaultOptions = {
      headers: this.getHeaders()
    }

    const config = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    }

    try {
      const response = await fetch(url, config)
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'API request failed')
      }
      
      return data
    } catch (error) {
      console.error('API Error:', error)
      throw error
    }
  }

  // Customer endpoints
  async getCustomers(page = 1, perPage = 10) {
    return this.request(`/admin/customer/get-all?page=${page}&per_page=${perPage}`)
  }

  // Cleaner endpoints
  async getCleaners(page = 1, perPage = 10) {
    return this.request(`/admin/cleaner/get-all?page=${page}&per_page=${perPage}`)
  }

  // Category endpoints
  async getCategories(page = 1, perPage = 10, approved = 0, isMain = false) {
    return this.request(`/admin/service-category/get-all?page=${page}&per_page=${perPage}&approved=${approved}&is_main=${isMain}`)
  }

  async createCategory(categoryData) {
    return this.request('/admin/service-category/create', {
      method: 'POST',
      body: JSON.stringify(categoryData)
    })
  }

  async updateCategory(categoryData) {
    return this.request('/admin/service-category/update', {
      method: 'POST',
      body: JSON.stringify(categoryData)
    })
  }

  async deleteCategory(categoryId) {
    return this.request('/admin/service-category/delete', {
      method: 'POST',
      body: JSON.stringify({ id: categoryId })
    })
  }
}

export default new ApiService()
